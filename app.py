from fastapi import FastAP<PERSON>, HTTPException, APIRouter
from pydantic import BaseModel
from telegram import Bot
import logging
import sqlite3
import re
from urllib.parse import urlparse, quote, parse_qs
import os

from youtube_downloader import YouTubeDownloader
import telebot
from send_video import send_video
import httpx
import uuid

logging.basicConfig(format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI()
router = APIRouter(prefix="/api")

class DownloadRequest(BaseModel):
    chat_id: int
    url: str
    bot_token: str

class InstagramClient:
    def __init__(self):
        self.api_base_url = "https://fastsaverapi.com/get-info"
        self.api_token = "knrZAJ5Q0FO4jNXW28e5q30t"

    def _extract_shortcode(self, url: str) -> str:
        """Extract shortcode from Instagram URL, handling different URL formats correctly"""
        parsed = urlparse(url)
        path_parts = [part for part in parsed.path.split('/') if part]  # Remove empty parts

        if len(path_parts) >= 2:
            # For URLs like /p/ABC123/, /reel/ABC123/, /share/ABC123/
            return path_parts[-1]  # Last non-empty part is the shortcode
        elif len(path_parts) == 1:
            # For URLs like /ABC123/
            return path_parts[0]
        else:
            raise ValueError(f"Could not extract shortcode from URL: {url}")

    async def get_media_info(self, url: str) -> dict:
        """Get media information from the external API"""
        try:
            encoded_url = quote(url, safe='')
            api_url = f"{self.api_base_url}?url={encoded_url}&token={self.api_token}"
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(api_url)
                response.raise_for_status()
                
                data = response.json()
                if data.get("error"):
                    raise Exception(f"API Error: {data.get('message', 'Unknown error')}")
                
                return data
        except httpx.RequestError as e:
            logger.error(f"Request error: {e}")
            raise Exception(f"Failed to connect to API: {e}")
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error: {e}")
            raise Exception(f"API returned error: {e}")
        except Exception as e:
            logger.error(f"Error getting media info: {e}")
            raise

    async def download_media(self, url: str, chat_id: int, bot: Bot, db_conn: sqlite3.Connection) -> None:
        if not is_valid_instagram_url(url):
            await bot.send_message(chat_id=chat_id, text="Invalid Instagram link. Please send a valid post URL.")
            return

        try:
            # Get media info from external API
            media_info = await self.get_media_info(url)
            
            # Send caption if available
            if media_info.get("caption"):
                await bot.send_message(chat_id=chat_id, text=f"Caption: {media_info['caption']}")

            # Handle different media types
            media_type = media_info.get("type", "").lower()
            download_url = media_info.get("download_url")
            thumb_url = media_info.get("thumb")
            medias = media_info.get("medias", [])
            
            c = db_conn.cursor()

            # Handle album posts (multiple media items)
            if media_type == "album" and medias:
                # Check if user requested a specific image index
                requested_index = extract_image_index(url)
                logger.info(f"Album detected with {len(medias)} items. Requested index: {requested_index}")
                logger.info(f"First few media items: {medias[:3]}")
                
                if requested_index >= 0 and requested_index < len(medias):
                    # User requested a specific image from the album
                    await bot.send_message(chat_id=chat_id, text=f"📸 Downloading specific image {requested_index + 1} from album...")
                    media_item = medias[requested_index]
                    logger.info(f"Selected media item: {media_item}")
                    
                    try:
                        item_type = media_item.get("type", "").lower()
                        item_download_url = media_item.get("download_url")
                        logger.info(f"Item type: {item_type}, Download URL: {item_download_url[:100]}...")
                        
                        if not item_download_url:
                            await bot.send_message(chat_id=chat_id, text="Selected image not found.")
                            return
                            
                        # Download the specific media file
                        async with httpx.AsyncClient(timeout=60.0) as client:
                            response = await client.get(item_download_url)
                            response.raise_for_status()
                            logger.info(f"Downloaded {len(response.content)} bytes")
                            
                            # Create downloads directory if it doesn't exist
                            os.makedirs("downloads", exist_ok=True)
                            
                            # Generate unique filename
                            file_id = str(uuid.uuid4())
                            
                            if item_type == "video":
                                file_path = f"downloads/{file_id}.mp4"
                            else:
                                file_path = f"downloads/{file_id}.jpg"
                            
                            # Save the file
                            with open(file_path, "wb") as f:
                                f.write(response.content)
                            logger.info(f"Saved file to {file_path}")
                            
                            # Send the file
                            if item_type == "video":
                                # Send video
                                with open(file_path, "rb") as video_file:
                                    msg = await bot.send_video(
                                        chat_id=chat_id, 
                                        video=video_file,
                                        caption=media_info.get("caption")
                                    )
                                logger.info(f"Video sent: {msg}")
                                if msg and msg.video:
                                    file_id = msg.video.file_id
                                    c.execute("INSERT INTO history (user_id, url, file_id, timestamp) VALUES (?, ?, ?, datetime('now'))", (chat_id, url, file_id))
                            elif item_type == "image":
                                # Send photo
                                with open(file_path, "rb") as photo_file:
                                    msg = await bot.send_photo(
                                        chat_id=chat_id, 
                                        photo=photo_file,
                                        caption=media_info.get("caption")
                                    )
                                logger.info(f"Photo sent: {msg}")
                                if msg and msg.photo:
                                    file_id = msg.photo[-1].file_id
                                    c.execute("INSERT INTO history (user_id, url, file_id, timestamp) VALUES (?, ?, ?, datetime('now'))", (chat_id, url, file_id))
                            
                            # Clean up the downloaded file
                            try:
                                os.remove(file_path)
                                logger.info(f"Cleaned up {file_path}")
                            except Exception as e:
                                logger.warning(f"Failed to clean up file {file_path}: {e}")
                                
                    except Exception as item_error:
                        logger.error(f"Error processing specific album item: {item_error}")
                        await bot.send_message(chat_id=chat_id, text=f"Error downloading the selected image: {str(item_error)}")
                        return
                    
                    db_conn.commit()
                    return
                else:
                    # Download all items in the album
                    await bot.send_message(chat_id=chat_id, text=f"📸 Album detected with {len(medias)} items. Downloading...")
                    
                    for i, media_item in enumerate(medias, 1):
                        try:
                            item_type = media_item.get("type", "").lower()
                            item_download_url = media_item.get("download_url")
                            logger.info(f"Processing album item {i}: type={item_type}, url={item_download_url[:100] if item_download_url else 'None'}...")
                            
                            if not item_download_url:
                                logger.warning(f"No download URL for item {i}")
                                continue
                                
                            # Download the media file locally first
                            async with httpx.AsyncClient(timeout=60.0) as client:
                                response = await client.get(item_download_url)
                                response.raise_for_status()
                                logger.info(f"Downloaded {len(response.content)} bytes for item {i}")
                                
                                # Create downloads directory if it doesn't exist
                                os.makedirs("downloads", exist_ok=True)
                                
                                # Generate unique filename
                                file_id = str(uuid.uuid4())
                                
                                if item_type == "video":
                                    file_path = f"downloads/{file_id}.mp4"
                                else:
                                    file_path = f"downloads/{file_id}.jpg"
                                
                                # Save the file
                                with open(file_path, "wb") as f:
                                    f.write(response.content)
                                logger.info(f"Saved item {i} to {file_path}")
                                
                                # Send the file
                                if item_type == "video":
                                    # Send video
                                    with open(file_path, "rb") as video_file:
                                        msg = await bot.send_video(
                                            chat_id=chat_id, 
                                            video=video_file,
                                            caption=f"Album item {i}/{len(medias)}" if len(medias) > 1 else None
                                        )
                                    logger.info(f"Video sent for item {i}: {msg}")
                                    if msg and msg.video:
                                        file_id = msg.video.file_id
                                        c.execute("INSERT INTO history (user_id, url, file_id, timestamp) VALUES (?, ?, ?, datetime('now'))", (chat_id, url, file_id))
                                elif item_type == "image":
                                    # Send photo
                                    with open(file_path, "rb") as photo_file:
                                        msg = await bot.send_photo(
                                            chat_id=chat_id, 
                                            photo=photo_file,
                                            caption=f"Album item {i}/{len(medias)}" if len(medias) > 1 else None
                                        )
                                    logger.info(f"Photo sent for item {i}: {msg}")
                                    if msg and msg.photo:
                                        file_id = msg.photo[-1].file_id
                                        c.execute("INSERT INTO history (user_id, url, file_id, timestamp) VALUES (?, ?, ?, datetime('now'))", (chat_id, url, file_id))
                                
                                # Clean up the downloaded file
                                try:
                                    os.remove(file_path)
                                    logger.info(f"Cleaned up {file_path}")
                                except Exception as e:
                                    logger.warning(f"Failed to clean up file {file_path}: {e}")
                                    
                        except Exception as item_error:
                            logger.error(f"Error processing album item {i}: {item_error}")
                            await bot.send_message(chat_id=chat_id, text=f"Error downloading album item {i}. Skipping...")
                            continue
                    
                    await bot.send_message(chat_id=chat_id, text="✅ Album download completed!")
                    db_conn.commit()
                    return
            
            # Handle single media posts (existing logic)
            if not download_url:
                await bot.send_message(chat_id=chat_id, text="No media found in the post.")
                return

            # Download the media file locally first
            try:
                async with httpx.AsyncClient(timeout=60.0) as client:
                    response = await client.get(download_url)
                    response.raise_for_status()
                    
                    # Create downloads directory if it doesn't exist
                    os.makedirs("downloads", exist_ok=True)
                    
                    # Generate unique filename
                    file_id = str(uuid.uuid4())
                    
                    if media_type == "video":
                        file_path = f"downloads/{file_id}.mp4"
                    else:
                        file_path = f"downloads/{file_id}.jpg"
                    
                    # Save the file
                    with open(file_path, "wb") as f:
                        f.write(response.content)
                    
                    # Send the file
                    if media_type == "video":
                        # Send video
                        with open(file_path, "rb") as video_file:
                            msg = await bot.send_video(
                                chat_id=chat_id, 
                                video=video_file,
                                caption=media_info.get("caption"),
                                duration=int(media_info.get("duration", 0)) if media_info.get("duration") else None
                            )
                        if msg and msg.video:
                            file_id = msg.video.file_id
                            c.execute("INSERT INTO history (user_id, url, file_id, timestamp) VALUES (?, ?, ?, datetime('now'))", (chat_id, url, file_id))
                    elif media_type == "image":
                        # Send photo
                        with open(file_path, "rb") as photo_file:
                            msg = await bot.send_photo(
                                chat_id=chat_id, 
                                photo=photo_file,
                                caption=media_info.get("caption")
                            )
                        if msg and msg.photo:
                            file_id = msg.photo[-1].file_id
                            c.execute("INSERT INTO history (user_id, url, file_id, timestamp) VALUES (?, ?, ?, datetime('now'))", (chat_id, url, file_id))
                    else:
                        # Try to send as photo first, then as video if that fails
                        try:
                            with open(file_path, "rb") as photo_file:
                                msg = await bot.send_photo(
                                    chat_id=chat_id, 
                                    photo=photo_file,
                                    caption=media_info.get("caption")
                                )
                            if msg and msg.photo:
                                file_id = msg.photo[-1].file_id
                                c.execute("INSERT INTO history (user_id, url, file_id, timestamp) VALUES (?, ?, ?, datetime('now'))", (chat_id, url, file_id))
                        except Exception:
                            # If photo fails, try as video
                            with open(file_path, "rb") as video_file:
                                msg = await bot.send_video(
                                    chat_id=chat_id, 
                                    video=video_file,
                                    caption=media_info.get("caption")
                                )
                            if msg and msg.video:
                                file_id = msg.video.file_id
                                c.execute("INSERT INTO history (user_id, url, file_id, timestamp) VALUES (?, ?, ?, datetime('now'))", (chat_id, url, file_id))
                    
                    # Clean up the downloaded file
                    try:
                        os.remove(file_path)
                    except Exception as e:
                        logger.warning(f"Failed to clean up file {file_path}: {e}")
                        
            except Exception as download_error:
                logger.error(f"Error downloading media file: {download_error}")
                await bot.send_message(chat_id=chat_id, text="Error downloading the media file. Please try again later.")

            db_conn.commit()

        except Exception as e:
            logger.error(f"Exception: {e}")
            await bot.send_message(chat_id=chat_id, text=f"Error downloading media: {str(e)}")

def init_db():
    conn = sqlite3.connect('sqlite3.db')
    c = conn.cursor()
    c.execute('''CREATE TABLE IF NOT EXISTS history 
                 (user_id INTEGER, url TEXT, file_id TEXT, timestamp TEXT)''')
    conn.commit()
    return conn

db_conn = init_db()
instagram_client = InstagramClient()
youtube_client = YouTubeDownloader()

def is_valid_instagram_url(url):
    pattern = r'https?://(www\.)?instagram\.com/(p|reel|tv|s|share)/[A-Za-z0-9_-]+/?(\?.*)?$'
    return bool(re.match(pattern, url))

def extract_image_index(url: str) -> int:
    """Extract image index from Instagram URL if present"""
    try:
        parsed = urlparse(url)
        query_params = parse_qs(parsed.query)
        img_index = query_params.get('img_index', [None])[0]
        if img_index:
            return int(img_index) - 1  # Convert to 0-based index
        return 0
    except (ValueError, KeyError):
        return 0

def is_valid_youtube_url(url):
    pattern = r'(?:https?://)?(?:www\.)?(?:youtube\.com/(?:watch\?v=|embed/|v/|shorts/)|youtu\.be/)([^"&?/ ]{11})'
    return re.match(pattern, url)

@router.post("/download")
async def download_media(request: DownloadRequest):
    try:
        bot = Bot(token=request.bot_token)
        telebot_instance = telebot.TeleBot(token=request.bot_token)
        
        if is_valid_instagram_url(request.url):
            await instagram_client.download_media(request.url, request.chat_id, bot, db_conn)
            return {"status": "success", "message": "Instagram media sent to Telegram chat."}
        elif is_valid_youtube_url(request.url):
            result = youtube_client.download_video(request.url)
            if result["success"]:
                try:
                    msg = send_video(
                        bot=telebot_instance,
                        chat_id=request.chat_id,
                        video_path=result["file_path"],
                        caption=result["title"],
                        duration=result.get("duration"),
                        width=result.get("width"),
                        height=result.get("height"),
                        thumbnail_path=result.get("thumbnail_path")
                    )
                    
                    if msg and msg.video:
                        c = db_conn.cursor()
                        c.execute(
                            "INSERT INTO history (user_id, url, file_id, timestamp) VALUES (?, ?, ?, datetime('now'))",
                            (request.chat_id, request.url, msg.video.file_id)
                        )
                        db_conn.commit()

                    # Clean up files
                    os.remove(result["file_path"])
                    if result.get("thumbnail_path") and os.path.exists(result["thumbnail_path"]):
                        os.remove(result["thumbnail_path"])
                    return {"status": "success", "message": "YouTube video sent to Telegram chat."}
                except Exception as e:
                    logger.error(f"Error sending video: {e}")
                    await bot.send_message(chat_id=request.chat_id, text=f"Error sending video: {str(e)}")
                    return {"status": "error", "message": f"Error sending video: {str(e)}"}
            else:
                await bot.send_message(chat_id=request.chat_id, text=f"Error downloading YouTube video: {result['message']}")
                return {"status": "error", "message": result["message"]}
        else:
            await bot.send_message(chat_id=request.chat_id, text="Invalid URL. Please send a valid Instagram or YouTube URL.")
            return {"status": "error", "message": "Invalid URL"}
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error processing request: {e}")
        raise HTTPException(status_code=500, detail="Failed to process request.")

app.include_router(router)

@app.on_event("startup")
async def startup_event():
    pass

@app.on_event("shutdown")
async def shutdown_event():
    pass

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=3333)
