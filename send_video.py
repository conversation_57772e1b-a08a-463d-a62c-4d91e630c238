"""
Functions for sending large video files using telebot with local API server.
"""
import logging
import telebot
from environs import Env
import os
from typing import Optional, Union

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

env = Env()
env.read_env()

LOCAL_API_URL = "http://localhost:8081/bot{0}/{1}"
FILE_SIZE_LIMIT = 1024
CONNECTION_TIMEOUT = 300

telebot.apihelper.API_URL = LOCAL_API_URL
telebot.apihelper.READ_TIMEOUT = CONNECTION_TIMEOUT
telebot.apihelper.CONNECT_TIMEOUT = CONNECTION_TIMEOUT

def send_video(
    bot: telebot.TeleBot,
    chat_id: Union[int, str],
    video_path: str,
    caption: Optional[str] = None,
    duration: Optional[int] = None,
    width: Optional[int] = None,
    height: Optional[int] = None,
    thumbnail_path: Optional[str] = None,
    supports_streaming: bool = True,
    disable_notification: bool = False,
    protect_content: bool = False,
    reply_to_message_id: Optional[int] = None,
    allow_sending_without_reply: bool = False,
    reply_markup: Optional[Union[telebot.types.InlineKeyboardMarkup, telebot.types.ReplyKeyboardMarkup]] = None
) -> telebot.types.Message:
    """
    Send a video file to a chat.

    Args:
        bot: Initialized telebot instance
        chat_id: Chat ID to send video to
        video_path: Path to the video file
        caption: Optional caption for the video
        duration: Optional duration of the video in seconds
        width: Optional width of the video
        height: Optional height of the video
        thumbnail_path: Optional path to thumbnail image
        supports_streaming: Whether the video supports streaming
        disable_notification: Whether to disable notification
        protect_content: Whether to protect the content
        reply_to_message_id: ID of the message to reply to
        allow_sending_without_reply: Whether to allow sending without reply
        reply_markup: Optional keyboard markup

    Returns:
        Message object containing the sent video information
    """
    try:
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"Video file not found: {video_path}")

        file_size = os.path.getsize(video_path) / (1024 * 1024)

        if file_size > 2048:  # 2GB in MB
            raise ValueError("Video file size exceeds 2GB limit")
            
        # Open and send the video
        with open(video_path, 'rb') as video:
            # Open thumbnail if provided
            thumbnail = None
            if thumbnail_path and os.path.exists(thumbnail_path):
                thumbnail = open(thumbnail_path, 'rb')

            try:
                return bot.send_video(
                    chat_id=chat_id,
                    video=video,
                    caption=caption,
                    duration=duration,
                    width=width,
                    height=height,
                    thumbnail=thumbnail,
                    supports_streaming=supports_streaming,
                    disable_notification=disable_notification,
                    protect_content=protect_content,
                    reply_to_message_id=reply_to_message_id,
                    allow_sending_without_reply=allow_sending_without_reply,
                    reply_markup=reply_markup
                )
            finally:
                if thumbnail:
                    thumbnail.close()
            
    except Exception as e:
        logger.error(f"Error sending video: {str(e)}")
        raise
