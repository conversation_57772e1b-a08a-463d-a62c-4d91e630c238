import os
import re
import json
import logging
import subprocess
from typing import Dict, Union, Optional

# Configure logging
logger = logging.getLogger(__name__)

class YouTubeDownloader:
    """Class for downloading YouTube videos and audio."""

    def __init__(self):
        """Initialize the YouTube downloader."""
        self.yt_dlp_available = self._check_yt_dlp()

    def _check_yt_dlp(self) -> bool:
        """Check if yt-dlp is available.

        Returns:
            bool: True if yt-dlp is available, False otherwise
        """
        try:
            subprocess.run(["which", "yt-dlp"], capture_output=True, text=True, check=False)
            return True
        except Exception as e:
            logger.error(f"Error checking for yt-dlp: {e}")
            return False

    def _check_ffprobe(self) -> bool:
        """Check if ffprobe is available.

        Returns:
            bool: True if ffprobe is available, False otherwise
        """
        try:
            subprocess.run(["which", "ffprobe"], capture_output=True, text=True, check=False)
            return True
        except Exception as e:
            logger.error(f"Error checking for ffprobe: {e}")
            return False

    def _extract_video_metadata(self, video_path: str) -> Dict[str, Union[int, float, None]]:
        """Extract video metadata using ffprobe.

        Args:
            video_path: Path to the video file

        Returns:
            Dict containing duration, width, height
        """
        metadata = {
            "duration": None,
            "width": None,
            "height": None
        }

        if not self._check_ffprobe():
            logger.warning("ffprobe not available, cannot extract video metadata")
            return metadata

        try:
            command = [
                "ffprobe",
                "-v", "quiet",
                "-print_format", "json",
                "-show_format",
                "-show_streams",
                video_path
            ]

            process = subprocess.run(command, capture_output=True, text=True, check=False)

            if process.returncode == 0:
                data = json.loads(process.stdout)

                # Find video stream
                for stream in data.get("streams", []):
                    if stream.get("codec_type") == "video":
                        metadata["width"] = stream.get("width")
                        metadata["height"] = stream.get("height")

                        # Get duration from stream or format
                        duration = stream.get("duration")
                        if duration:
                            metadata["duration"] = int(float(duration))
                        break

                # If duration not found in stream, try format
                if not metadata["duration"]:
                    format_info = data.get("format", {})
                    duration = format_info.get("duration")
                    if duration:
                        metadata["duration"] = int(float(duration))

        except Exception as e:
            logger.error(f"Error extracting video metadata: {e}")

        return metadata

    def _generate_thumbnail(self, video_path: str, thumbnail_path: str) -> bool:
        """Generate a thumbnail from the video.

        Args:
            video_path: Path to the video file
            thumbnail_path: Path where thumbnail should be saved

        Returns:
            bool: True if thumbnail was generated successfully
        """
        if not self._check_ffprobe():
            logger.warning("ffmpeg not available, cannot generate thumbnail")
            return False

        try:
            command = [
                "ffmpeg",
                "-i", video_path,
                "-ss", "00:00:01.000",  # Take screenshot at 1 second
                "-vframes", "1",
                "-y",  # Overwrite output file
                thumbnail_path
            ]

            process = subprocess.run(command, capture_output=True, text=True, check=False)

            if process.returncode == 0 and os.path.exists(thumbnail_path):
                return True
            else:
                logger.error(f"Error generating thumbnail: {process.stderr}")
                return False

        except Exception as e:
            logger.error(f"Error generating thumbnail: {e}")
            return False

    def _extract_video_id(self, url: str) -> Optional[str]:
        """Extract the video ID from a YouTube URL.

        Args:
            url: The YouTube URL

        Returns:
            The video ID or None if not found
        """
        # Regular expression to match various YouTube URL formats
        patterns = [
            r'(?:youtube\.com/(?:[^/]+/.+/|(?:v|e(?:mbed)?|shorts)/|.*[?&]v=)|youtu\.be/)([^"&?/ ]{11})',
            r'(?:www\.)?youtube\.com/watch\?v=([^&]+)',
            r'(?:www\.)?youtu\.be/([^?]+)',
            r'(?:www\.)?youtube\.com/embed/([^?]+)',
            r'(?:www\.)?youtube\.com/v/([^?]+)',
            r'(?:www\.)?youtube\.com/shorts/([^?]+)',
            r'(?:www\.)?youtube\.com/user/[^/]+/\?v=([^&]+)',
            r'(?:www\.)?youtube\.com/attribution_link\?.*?u=/watch%3Fv%3D([^%&]+)',
            r'(?:www\.)?youtube\.com/attribution_link\?.*?u=%2Fwatch%3Fv%3D([^%&]+)',
            r'(?:www\.)?youtube\.com/attribution_link\?.*?u=([^&]+)'
        ]

        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        return None

    def download_video(self, url: str) -> Dict[str, Union[str, bool, int, None]]:
        """Download a YouTube video.

        Args:
            url: The YouTube URL

        Returns:
            Dict with download information including metadata
        """
        result = {
            "success": False,
            "message": "",
            "file_path": "",
            "title": "",
            "duration": None,
            "width": None,
            "height": None,
            "thumbnail_path": None
        }

        if not self.yt_dlp_available:
            result["message"] = "yt-dlp is not available"
            return result

        try:
            # Create a temporary directory for the download
            timestamp = int(os.path.getmtime(__file__))
            download_dir = os.path.join("downloads")
            os.makedirs(download_dir, exist_ok=True)

            # Extract the video ID
            video_id = self._extract_video_id(url)
            if not video_id:
                result["message"] = "Could not extract video ID from URL"
                return result

            # Set the output template
            output_template = os.path.join(download_dir, f"youtube_{video_id}_{timestamp}.%(ext)s")

            # Download the video
            command = [
                "yt-dlp",
                "-f", "best[ext=mp4]/best",
                "-o", output_template,
                "--no-playlist",
                "--no-warnings",
                "--quiet",
                url
            ]

            process = subprocess.run(command, capture_output=True, text=True, check=False)

            if process.returncode != 0:
                result["message"] = f"Error downloading video: {process.stderr}"
                return result

            # Find the downloaded file
            expected_file = os.path.join(download_dir, f"youtube_{video_id}_{timestamp}.mp4")
            if not os.path.exists(expected_file):
                # Try to find any file with the video ID and timestamp
                for file in os.listdir(download_dir):
                    if f"youtube_{video_id}_{timestamp}" in file:
                        expected_file = os.path.join(download_dir, file)
                        break

            if not os.path.exists(expected_file):
                result["message"] = "Could not find downloaded file"
                return result

            # Get the video title
            title_command = [
                "yt-dlp",
                "--get-title",
                "--no-playlist",
                "--no-warnings",
                "--quiet",
                url
            ]

            title_process = subprocess.run(title_command, capture_output=True, text=True, check=False)
            title = title_process.stdout.strip()

            # Extract video metadata
            metadata = self._extract_video_metadata(expected_file)

            # Generate thumbnail
            thumbnail_path = expected_file.replace(".mp4", "_thumb.jpg")
            thumbnail_generated = self._generate_thumbnail(expected_file, thumbnail_path)

            # Set the result
            result["success"] = True
            result["file_path"] = expected_file
            result["title"] = title
            result["duration"] = metadata["duration"]
            result["width"] = metadata["width"]
            result["height"] = metadata["height"]
            result["thumbnail_path"] = thumbnail_path if thumbnail_generated else None

            return result

        except Exception as e:
            logger.error(f"Error downloading video: {e}")
            result["message"] = f"Error: {str(e)}"
            return result

    def download_audio(self, url: str) -> Dict[str, Union[str, bool]]:
        """Download a YouTube video as audio.

        Args:
            url: The YouTube URL

        Returns:
            Dict with download information
        """
        result = {
            "success": False,
            "message": "",
            "file_path": "",
            "title": ""
        }

        if not self.yt_dlp_available:
            result["message"] = "yt-dlp is not available"
            return result

        try:
            # Create a temporary directory for the download
            timestamp = int(os.path.getmtime(__file__))
            download_dir = os.path.join("downloads")
            os.makedirs(download_dir, exist_ok=True)

            # Extract the video ID
            video_id = self._extract_video_id(url)
            if not video_id:
                result["message"] = "Could not extract video ID from URL"
                return result

            # Set the output template
            output_template = os.path.join(download_dir, f"youtube_{video_id}_{timestamp}.%(ext)s")

            # Download the audio directly as mp3
            command = [
                "yt-dlp",
                "-f", "bestaudio[ext=m4a]/bestaudio",
                "-o", output_template.replace(".%(ext)s", ".mp3"),
                "--no-playlist",
                "--no-warnings",
                "--quiet",
                url
            ]

            process = subprocess.run(command, capture_output=True, text=True, check=False)

            if process.returncode != 0:
                result["message"] = f"Error downloading audio: {process.stderr}"
                return result

            # Find the downloaded file
            expected_file = os.path.join(download_dir, f"youtube_{video_id}_{timestamp}.mp3")
            if not os.path.exists(expected_file):
                # Try to find any file with the video ID and timestamp
                for file in os.listdir(download_dir):
                    if f"youtube_{video_id}_{timestamp}" in file:
                        expected_file = os.path.join(download_dir, file)
                        break

            if not os.path.exists(expected_file):
                result["message"] = "Could not find downloaded file"
                return result

            # Get the video title
            title_command = [
                "yt-dlp",
                "--get-title",
                "--no-playlist",
                "--no-warnings",
                "--quiet",
                url
            ]

            title_process = subprocess.run(title_command, capture_output=True, text=True, check=False)
            title = title_process.stdout.strip()

            # Set the result
            result["success"] = True
            result["file_path"] = expected_file
            result["title"] = title

            return result

        except Exception as e:
            logger.error(f"Error downloading audio: {e}")
            result["message"] = f"Error: {str(e)}"
            return result 